<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Notes - Tadabbur AI</title>
    <link rel="stylesheet" href="styles/global.css">
    <link rel="stylesheet" href="styles/study.css">
    <link rel="stylesheet" href="styles/notes.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="study-layout">
        <!-- Left Sidebar -->
        <aside class="sidebar" id="sidebar">
            <!-- App Logo <PERSON>er -->
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../docs/logo.png" alt="Tadabbur AI" class="logo-img">
                    <span><PERSON><PERSON>bur AI</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Folders Section -->
            <div class="folders-section">
                <div class="section-header">
                    <i class="fas fa-folder"></i>
                    <span>Folders</span>
                </div>
                
                <div class="folder-list">
                    <div class="folder-item active" onclick="openFolder('all-notes')">
                        <i class="fas fa-sticky-note"></i>
                        <span>All notes</span>
                        <span class="count">(4)</span>
                    </div>
                    <div class="folder-item" onclick="openFolder('bio')">
                        <i class="fas fa-user"></i>
                        <span>bio</span>
                        <span class="count">(1)</span>
                    </div>
                </div>
                
                <button class="create-folder-btn">
                    <i class="fas fa-plus"></i>
                    <span>Create new folder</span>
                </button>
            </div>

            <!-- Bottom Section -->
            <div class="sidebar-bottom">
                <!-- Support Link -->
                <div class="support-section">
                    <button class="support-btn">
                        <i class="fas fa-question-circle"></i>
                        <span>Support</span>
                    </button>
                </div>

                <!-- Upgrade Section -->
                <div class="upgrade-section">
                    <button class="upgrade-btn">
                        <i class="fas fa-crown"></i>
                        <span>Upgrade plan</span>
                    </button>
                    <p class="upgrade-text">Get more features and unlimited access</p>
                    <div class="usage-indicator">
                        <span class="usage-text">3 / 3 Notes free</span>
                        <div class="usage-bar">
                            <div class="usage-fill"></div>
                        </div>
                    </div>
                </div>

                <!-- Profile Section -->
                <div class="profile-section">
                    <div class="profile-info">
                        <div class="profile-avatar">
                            <span>MR</span>
                        </div>
                        <div class="profile-details">
                            <div class="profile-name">Muazam Rashid</div>
                            <div class="profile-email">muazamr99@gmail.c...</div>
                        </div>
                        <button class="profile-settings">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="notes-main">
            <!-- Header with Breadcrumb -->
            <header class="notes-header">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <span class="breadcrumb-item">All notes</span>
                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                    <span class="breadcrumb-item current">My notes</span>
                </div>
                <div class="header-actions">
                    <button class="btn btn-ghost">
                        <i class="fas fa-filter"></i>
                    </button>
                    <button class="btn btn-ghost">
                        <i class="fas fa-sort"></i>
                    </button>
                    <div class="user-menu">
                        <button class="user-menu-btn">
                            <i class="fas fa-user-circle"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Notes List -->
            <div class="notes-container">
                <!-- New Chat Section -->
                <div class="new-chat-section">
                    <div class="new-chat-header">
                        <h3>New Chat</h3>
                    </div>
                    <form class="new-chat-form" id="newChatForm">
                        <div class="new-chat-input-wrapper">
                            <input
                                type="text"
                                class="new-chat-input"
                                placeholder="Ask about any verse, concept, or story from the Quran..."
                                id="newChatInput"
                                required
                            >
                        </div>
                        <button type="submit" class="new-chat-submit">
                            <i class="fas fa-arrow-right"></i>
                            Start Chat
                        </button>
                    </form>
                </div>

                <div class="notes-list">
                    <!-- Note Item 1 -->
                    <div class="note-item" onclick="openNote('verses-patience')">
                        <div class="note-content">
                            <h3 class="note-title">Verses about Patience</h3>
                            <p class="note-description">
                                Study notes on Quranic verses about patience (sabr), including Al-Baqarah 2:155 and Az-Zumar 39:10. Contains flashcards and quiz questions.
                            </p>
                            <div class="note-meta">
                                <span class="note-folder">
                                    <i class="fas fa-folder"></i>
                                    All notes
                                </span>
                                <span class="note-date">2 hours ago</span>
                            </div>
                        </div>
                        <div class="note-actions">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <!-- Note Item 2 -->
                    <div class="note-item" onclick="openNote('surah-fatiha')">
                        <div class="note-content">
                            <h3 class="note-title">Surah Al-Fatiha Explanation</h3>
                            <p class="note-description">
                                Comprehensive study of Surah Al-Fatiha with verse-by-verse analysis, tafsir references, and memorization aids.
                            </p>
                            <div class="note-meta">
                                <span class="note-folder">
                                    <i class="fas fa-folder"></i>
                                    All notes
                                </span>
                                <span class="note-date">Yesterday</span>
                            </div>
                        </div>
                        <div class="note-actions">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <!-- Note Item 3 -->
                    <div class="note-item" onclick="openNote('prophet-yusuf')">
                        <div class="note-content">
                            <h3 class="note-title">Stories of Prophet Yusuf</h3>
                            <p class="note-description">
                                Key lessons from the story of Prophet Yusuf (AS) including themes of patience, forgiveness, and divine wisdom.
                            </p>
                            <div class="note-meta">
                                <span class="note-folder">
                                    <i class="fas fa-user"></i>
                                    bio
                                </span>
                                <span class="note-date">3 days ago</span>
                            </div>
                        </div>
                        <div class="note-actions">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <!-- Note Item 4 -->
                    <div class="note-item" onclick="openNote('jihad-meaning')">
                        <div class="note-content">
                            <h3 class="note-title">Understanding Jihad in Islam</h3>
                            <p class="note-description">
                                Detailed explanation of the concept of jihad in Islamic context, different types, and Quranic references.
                            </p>
                            <div class="note-meta">
                                <span class="note-folder">
                                    <i class="fas fa-folder"></i>
                                    All notes
                                </span>
                                <span class="note-date">1 week ago</span>
                            </div>
                        </div>
                        <div class="note-actions">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- Empty State (hidden by default) -->
                <div class="empty-state" style="display: none;">
                    <div class="empty-content">
                        <i class="fas fa-sticky-note"></i>
                        <h3>No notes found</h3>
                        <p>Start a conversation to create your first study note</p>
                        <a href="index.html" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Start New Chat
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <script>
        // Mobile menu functionality
        document.getElementById('mobileMenuBtn').addEventListener('click', function() {
            document.getElementById('sidebar').classList.add('open');
            document.getElementById('mobileOverlay').classList.add('show');
            document.body.style.overflow = 'hidden';
        });

        document.getElementById('sidebarToggle').addEventListener('click', closeSidebar);
        document.getElementById('mobileOverlay').addEventListener('click', closeSidebar);

        function closeSidebar() {
            document.getElementById('sidebar').classList.remove('open');
            document.getElementById('mobileOverlay').classList.remove('show');
            document.body.style.overflow = '';
        }

        // New chat functionality
        document.getElementById('newChatForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const query = document.getElementById('newChatInput').value.trim();
            if (query) {
                // Navigate to study page with the query
                window.location.href = 'study.html?q=' + encodeURIComponent(query);
            }
        });

        // Note click handler
        function openNote(noteId) {
            // In real app, this would navigate to the specific note
            window.location.href = 'study.html?note=' + noteId;
        }

        // Folder navigation
        function openFolder(folderId) {
            // Update URL and reload with new folder
            const url = new URL(window.location);
            url.searchParams.set('folder', folderId);
            window.location.href = url.toString();
        }

        // Handle folder clicks for visual feedback
        document.querySelectorAll('.folder-item').forEach(item => {
            item.addEventListener('click', function(e) {
                // Prevent the onclick from firing twice
                e.stopPropagation();

                // Remove active class from all items
                document.querySelectorAll('.folder-item').forEach(i => i.classList.remove('active'));
                // Add active class to clicked item
                this.classList.add('active');

                // Update breadcrumb
                const folderName = this.querySelector('span:first-of-type').textContent;
                document.querySelector('.breadcrumb-item.current').textContent = folderName;
            });
        });
    </script>
</body>
</html>
