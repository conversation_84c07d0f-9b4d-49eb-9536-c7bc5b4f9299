// User types
export interface User {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string
  createdAt: Date
  updatedAt: Date
}

// Folder types
export interface Folder {
  id: number
  userId: string
  name: string
  noteCount: number
  color: string
  createdAt: Date
  updatedAt: Date
}

// Note types
export interface Note {
  id: number
  verseKeys: string
  userId: string
  folderId?: number
  uploadId?: number
  tafsirIds?: number[]
  asbabIds?: string[]
  title: string
  shortDescription?: string
  content?: string
  language?: string
  createdAt: Date
  updatedAt: Date
}

// Chat message types
export interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

// Verse types
export interface Verse {
  id: number
  surahNumber: number
  ayahNumber: number
  verseKeys: string
  textArabic: string
  textClean?: string
}

// Tafsir types
export interface Tafsir {
  id: number
  kitabId: number
  verseKeys: string
  text: string
  language?: string
  created_at: Date
  updated_at: Date
}

// Flash card types
export interface FlashCard {
  id: number
  userId?: string
  noteId?: number
  question: string
  answer: string
  createdAt: Date
}

// Quiz types
export interface Quiz {
  id: number
  userId?: string
  noteId?: number
  question: string
  quizAnswers: any
  createdAt: Date
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Search types
export interface SearchResult {
  verses: Verse[]
  tafsirs: Tafsir[]
  notes: Note[]
}
