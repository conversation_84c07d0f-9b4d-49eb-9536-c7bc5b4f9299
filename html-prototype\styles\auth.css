/* Authentication Pages Styles */

/* Header */
.auth-header {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-light);
    padding: var(--spacing-md) 0;
}

.auth-header .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    text-decoration: none;
}

.auth-header .logo .logo-img {
    height: 32px;
    width: auto;
}

/* Main Layout */
.auth-main {
    min-height: calc(100vh - 80px);
    display: flex;
    align-items: center;
    padding: var(--spacing-xl) 0;
    background: linear-gradient(135deg, var(--surface-color) 0%, var(--background-color) 100%);
}

.auth-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

/* Auth Card */
.auth-card {
    background-color: var(--background-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    max-width: 480px;
    width: 100%;
    margin: 0 auto;
}

.auth-header-content {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.auth-header-content h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.auth-header-content p {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

/* Form Styles */
.auth-form {
    margin-bottom: var(--spacing-xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: var(--text-secondary);
}

.password-strength {
    margin-top: var(--spacing-xs);
    font-size: 0.8125rem;
}

.strength-weak { color: var(--error-color); }
.strength-medium { color: var(--warning-color); }
.strength-strong { color: var(--success-color); }

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.forgot-link:hover {
    color: var(--primary-hover);
}

/* Submit Button */
.auth-submit {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    position: relative;
}

.auth-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Form Error */
.form-error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: var(--error-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    text-align: center;
}

/* Divider */
.auth-divider {
    position: relative;
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--border-color);
}

.auth-divider span {
    background-color: var(--background-color);
    color: var(--text-muted);
    padding: 0 var(--spacing-md);
    font-size: 0.875rem;
}

/* Social Login */
.social-login {
    margin-bottom: var(--spacing-xl);
}

.social-btn {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.6;
    cursor: not-allowed;
}

/* Auth Switch */
.auth-switch {
    text-align: center;
}

.auth-switch p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

.auth-switch a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.auth-switch a:hover {
    color: var(--primary-hover);
}

/* Auth Info Panel */
.auth-info {
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-content {
    max-width: 400px;
}

.info-content h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.info-content > p {
    color: var(--text-secondary);
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.feature-item i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.25rem;
}

.feature-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .auth-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .auth-info {
        order: -1;
    }

    .info-content {
        text-align: center;
    }

    .info-content h2 {
        font-size: 1.75rem;
    }
}

@media (max-width: 768px) {
    .auth-main {
        padding: var(--spacing-lg) 0;
    }

    .auth-card {
        padding: var(--spacing-xl);
        margin: 0 var(--spacing-md);
    }

    .auth-header-content h1 {
        font-size: 1.75rem;
    }

    .form-options {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .info-content h2 {
        font-size: 1.5rem;
    }

    .info-content > p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: var(--spacing-lg);
        border-radius: var(--radius-lg);
    }

    .auth-header-content h1 {
        font-size: 1.5rem;
    }

    .checkbox-container {
        font-size: 0.8125rem;
    }
}
