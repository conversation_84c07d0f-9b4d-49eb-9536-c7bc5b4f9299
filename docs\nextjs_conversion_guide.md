# Next.js Conversion Rules & Structure Guide

## Project Structure Requirements

```
project-root/
├── src/
│   ├── app/                    # App router (Next.js 13+)
│   │   ├── layout.tsx         # Root layout
│   │   ├── page.tsx           # Home page
│   │   ├── globals.css        # Global styles with Tailwind
│   │   └── [feature]/         # Feature-based routing
│   ├── components/            # Reusable UI components
│   │   ├── ui/               # Base UI components
│   │   └── [feature]/        # Feature-specific components
│   ├── lib/                  # Utility functions and configurations
│   │   ├── db/               # Database related
│   │   │   ├── schema.ts     # Drizzle schema definitions
│   │   │   ├── index.ts      # Database connection
│   │   │   └── migrations/   # Database migrations
│   │   ├── auth/             # Better Auth configuration
│   │   │   ├── config.ts     # Better Auth setup
│   │   │   └── client.ts     # Auth client utilities
│   │   └── utils.ts          # General utilities
│   ├── hooks/                # Custom React hooks
│   └── types/                # TypeScript type definitions
├── public/                   # Static assets
├── drizzle.config.ts        # Drizzle configuration
└── package.json
```

## Conversion Rules

### 1. HTML to Next.js Components
- Convert HTML files to React components (.tsx files)
- Use Next.js App Router (not Pages Router)
- Replace `<html>`, `<head>`, `<body>` with Next.js layout system
- Convert class attributes to className
- Use Next.js `<Image>` component instead of `<img>`
- Use Next.js `<Link>` component instead of `<a>` for internal navigation

### 2. CSS to Tailwind Conversion
- Replace all custom CSS classes with Tailwind utility classes
- Move global styles to `src/app/globals.css`
- Use Tailwind's responsive prefixes (sm:, md:, lg:, xl:, 2xl:)
- Convert CSS Grid and Flexbox to Tailwind equivalents
- Use Tailwind's color palette instead of custom colors
- Replace media queries with Tailwind responsive utilities

### 3. JavaScript to TypeScript React
- Convert all .js files to .tsx files
- Add proper TypeScript interfaces for all data structures
- Use React hooks (useState, useEffect, etc.) instead of vanilla JS DOM manipulation
- Convert event handlers to React event handlers
- Use React's state management instead of global variables

### 4. Database Integration Rules

#### Better Auth Setup
- Install Better Auth and configure with chosen providers
- Set up Better Auth database adapter for Drizzle ORM
- Configure authentication providers in Better Auth config
- Use Better Auth's built-in database schema or extend it
- Implement Better Auth middleware for route protection

#### Supabase Database Only
- Use Supabase only as a PostgreSQL database provider
- Connect Drizzle ORM directly to Supabase database
- Remove Supabase Auth dependencies
- Use Supabase database URL in Drizzle configuration

#### Drizzle ORM Schema Rules
- Define all database tables in `src/lib/db/schema.ts`
- Use proper Drizzle column types (text, integer, timestamp, etc.)
- Define relationships using Drizzle relations
- Create proper indexes for performance
- Use camelCase for schema field names

```typescript
// Example schema structure
export const users = pgTable('users', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: text('email').notNull().unique(),
  createdAt: timestamp('created_at').defaultNow(),
});
```

### 5. Authentication Implementation (Better Auth)
- Use Better Auth instead of Supabase Auth
- Install and configure Better Auth with your chosen providers
- Create protected routes using Better Auth middleware
- Implement proper session management with Better Auth
- Use Better Auth React hooks for auth state management
- Configure Better Auth database adapter for Drizzle ORM
- Set up authentication providers (Google, GitHub, email/password, etc.)
- Create auth schema in Drizzle for Better Auth tables

### 6. Data Fetching Patterns
- Use Server Components for initial data loading
- Use Client Components for interactive features
- Implement proper loading and error states
- Use React Query or SWR for client-side data fetching
- Follow Next.js caching strategies

### 7. Component Architecture Rules
- Create reusable UI components in `components/ui/`
- Use composition over inheritance
- Implement proper prop typing with TypeScript
- Follow single responsibility principle
- Use React.forwardRef for components that need refs

### 8. Performance Optimization
- Use Next.js Image optimization
- Implement proper code splitting
- Use dynamic imports for heavy components
- Optimize bundle size with proper imports
- Implement proper SEO with Next.js metadata API

## Configuration Files Required

### 1. tailwind.config.js
- Configure content paths
- Extend theme if needed
- Add custom utilities if required

### 2. drizzle.config.ts
- Database connection configuration
- Migration settings
- Schema path configuration

### 3. next.config.js
- Image domains configuration
- Environment variables setup
- Build optimization settings

### 4. Environment Variables (.env.local)
```
DATABASE_URL=your_supabase_database_url
BETTER_AUTH_SECRET=your_better_auth_secret
BETTER_AUTH_URL=http://localhost:3000
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
# Add other OAuth provider credentials as needed
```

## Migration Strategy

### Phase 1: Setup & Structure
1. Initialize Next.js project with TypeScript
2. Install and configure Tailwind CSS
3. Set up Supabase project and Drizzle ORM
4. Create project structure

### Phase 2: Component Conversion
1. Convert HTML pages to Next.js pages/components
2. Transform CSS to Tailwind classes
3. Convert JavaScript logic to React hooks
4. Implement TypeScript types

### Phase 3: Database & Authentication Integration
1. Design and implement Drizzle schema (including Better Auth tables)
2. Set up Supabase database connection
3. Configure Better Auth with Drizzle adapter
4. Create database migration files
5. Implement data access patterns

### Phase 4: Feature Implementation
1. Configure Better Auth providers and routes
2. Add data fetching logic
3. Create API routes if needed
4. Add error handling and loading states

### Phase 5: Optimization
1. Optimize performance
2. Add proper SEO
3. Implement caching strategies
4. Test and deploy

## Best Practices

### Code Organization
- Group related components together
- Use barrel exports (index.ts files)
- Keep components small and focused
- Separate business logic from UI logic

### TypeScript Usage
- Define interfaces for all props and data structures
- Use strict mode
- Avoid 'any' type
- Create custom types for domain models

### Error Handling
- Implement proper error boundaries
- Handle database errors gracefully
- Show user-friendly error messages
- Log errors for debugging

### Security
- Validate all user inputs
- Use Supabase RLS policies
- Sanitize data before database operations
- Implement proper authentication checks

This guide provides a comprehensive framework for converting your existing web app to the modern Next.js stack while maintaining code quality and following best practices.