{"designSystem": {"name": "AI Chat Interface Design System", "version": "1.0.0", "description": "Modern AI chat interface with sidebar navigation and responsive grid layouts", "colorPalette": {"primary": "#97B067", "primaryHover": "#7A9A52", "primaryLight": "#B5C889", "background": {"main": "#FFFFFF", "secondary": "#F8F9FA", "tertiary": "#F3F4F6"}, "text": {"primary": "#1F2937", "secondary": "#6B7280", "muted": "#9CA3AF", "inverse": "#FFFFFF"}, "border": {"light": "#E5E7EB", "medium": "#D1D5DB", "dark": "#9CA3AF"}, "status": {"success": "#10B981", "warning": "#F59E0B", "error": "#EF4444", "info": "#3B82F6"}}, "typography": {"fontFamily": {"primary": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "monospace": "'SF Mono', Monaco, 'Cascadia Code', monospace"}, "fontSize": {"xs": "12px", "sm": "14px", "base": "16px", "lg": "18px", "xl": "20px", "2xl": "24px"}, "fontWeight": {"normal": 400, "medium": 500, "semibold": 600, "bold": 700}, "lineHeight": {"tight": 1.25, "normal": 1.5, "relaxed": 1.75}}, "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "2xl": "48px", "3xl": "64px"}, "borderRadius": {"sm": "4px", "md": "8px", "lg": "12px", "xl": "16px", "full": "9999px"}, "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1)"}, "layout": {"sidebar": {"width": "280px", "minWidth": "240px", "maxWidth": "320px", "backgroundColor": "#FFFFFF", "borderRight": "1px solid #E5E7EB", "padding": "16px"}, "mainContent": {"flex": 1, "backgroundColor": "#F8F9FA", "padding": "24px", "overflow": "auto"}, "rightPanel": {"width": "320px", "backgroundColor": "#FFFFFF", "borderLeft": "1px solid #E5E7EB", "padding": "16px"}}, "components": {"sidebar": {"structure": {"header": {"logo": {"size": "32px", "borderRadius": "8px", "marginBottom": "24px"}, "title": {"fontSize": "18px", "fontWeight": 600, "color": "#1F2937"}}, "navigation": {"itemHeight": "40px", "itemPadding": "8px 12px", "itemBorderRadius": "8px", "itemSpacing": "4px", "activeBackground": "#97B067", "activeColor": "#FFFFFF", "hoverBackground": "#F3F4F6", "iconSize": "20px", "iconMarginRight": "12px"}, "userSection": {"position": "bottom", "avatar": {"size": "32px", "borderRadius": "50%"}, "userInfo": {"fontSize": "14px", "fontWeight": 500}}}}, "chatInterface": {"structure": {"header": {"height": "60px", "padding": "16px 24px", "backgroundColor": "#FFFFFF", "borderBottom": "1px solid #E5E7EB", "title": {"fontSize": "18px", "fontWeight": 600}, "subtitle": {"fontSize": "14px", "color": "#6B7280"}}, "messageArea": {"padding": "24px", "gap": "16px", "maxWidth": "800px", "margin": "0 auto"}, "inputArea": {"position": "sticky", "bottom": 0, "padding": "16px 24px", "backgroundColor": "#FFFFFF", "borderTop": "1px solid #E5E7EB"}}}, "messageCard": {"structure": {"padding": "16px", "borderRadius": "12px", "marginBottom": "16px", "user": {"backgroundColor": "#F3F4F6", "alignSelf": "flex-end", "maxWidth": "80%"}, "ai": {"backgroundColor": "#FFFFFF", "border": "1px solid #E5E7EB", "alignSelf": "flex-start", "maxWidth": "85%"}, "avatar": {"size": "32px", "borderRadius": "50%", "marginRight": "12px"}}}, "button": {"variants": {"primary": {"backgroundColor": "#97B067", "color": "#FFFFFF", "border": "none", "padding": "8px 16px", "borderRadius": "8px", "fontSize": "14px", "fontWeight": 500, "cursor": "pointer", "hover": {"backgroundColor": "#7A9A52"}}, "secondary": {"backgroundColor": "#F3F4F6", "color": "#374151", "border": "1px solid #D1D5DB", "padding": "8px 16px", "borderRadius": "8px", "fontSize": "14px", "fontWeight": 500, "cursor": "pointer", "hover": {"backgroundColor": "#E5E7EB"}}, "ghost": {"backgroundColor": "transparent", "color": "#6B7280", "border": "none", "padding": "8px 16px", "borderRadius": "8px", "fontSize": "14px", "fontWeight": 500, "cursor": "pointer", "hover": {"backgroundColor": "#F3F4F6"}}}}, "imageGrid": {"structure": {"display": "grid", "gridTemplateColumns": "repeat(auto-fill, minmax(120px, 1fr))", "gap": "12px", "padding": "16px", "item": {"aspectRatio": "1:1", "borderRadius": "8px", "overflow": "hidden", "border": "1px solid #E5E7EB", "cursor": "pointer", "hover": {"transform": "scale(1.02)", "transition": "transform 0.2s ease"}}}}, "tagSystem": {"structure": {"display": "flex", "flexWrap": "wrap", "gap": "8px", "marginBottom": "16px", "tag": {"padding": "4px 12px", "borderRadius": "16px", "fontSize": "12px", "fontWeight": 500, "backgroundColor": "#F3F4F6", "color": "#374151", "border": "1px solid #E5E7EB", "cursor": "pointer", "active": {"backgroundColor": "#97B067", "color": "#FFFFFF", "border": "1px solid #97B067"}}}}, "inputField": {"structure": {"padding": "12px 16px", "borderRadius": "8px", "border": "1px solid #D1D5DB", "fontSize": "14px", "backgroundColor": "#FFFFFF", "width": "100%", "resize": "none", "focus": {"outline": "none", "borderColor": "#97B067", "boxShadow": "0 0 0 3px rgba(151, 176, 103, 0.1)"}, "placeholder": {"color": "#9CA3AF"}}}}, "patterns": {"threeColumnLayout": {"display": "flex", "height": "100vh", "overflow": "hidden", "sidebar": "280px", "mainContent": "flex-1", "rightPanel": "320px"}, "responsiveBreakpoints": {"mobile": "768px", "tablet": "1024px", "desktop": "1280px", "behavior": {"mobile": {"sidebar": "hidden by default, overlay when opened", "rightPanel": "hidden", "mainContent": "full width"}, "tablet": {"sidebar": "collapsible", "rightPanel": "hidden", "mainContent": "flexible width"}, "desktop": {"sidebar": "always visible", "rightPanel": "toggleable", "mainContent": "flexible width"}}}, "interactionStates": {"hover": {"transition": "all 0.2s ease-in-out", "transform": "translateY(-1px) or scale(1.02)", "boxShadow": "elevated shadow"}, "active": {"transform": "translateY(0) or scale(0.98)", "transition": "all 0.1s ease-in-out"}, "focus": {"outline": "none", "boxShadow": "0 0 0 3px primary color with 0.1 opacity"}}}, "accessibilityGuidelines": {"colorContrast": "WCAG AA compliant (4.5:1 ratio minimum)", "focusIndicators": "Visible focus rings with primary color", "keyboardNavigation": "All interactive elements accessible via keyboard", "semanticHTML": "Proper heading hierarchy and ARIA labels", "responsiveText": "Scalable font sizes for different screen sizes"}, "implementationNotes": {"cssVariables": "Use CSS custom properties for theme switching", "componentLibrary": "Build reusable components with consistent styling", "stateManagement": "Maintain UI state for sidebar collapse, theme selection", "animations": "Subtle transitions for better UX (0.2s ease-in-out)", "gridSystems": "Use CSS Grid for image galleries and card layouts", "flexboxLayouts": "Use Flexbox for navigation and button groups"}}}